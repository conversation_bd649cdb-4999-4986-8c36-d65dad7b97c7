# PDF Converter API

The PDF Converter API allows you to convert regular PDF documents into "scanned" PDFs where each page is rendered as a high-resolution image. This simulates the effect of physically scanning a document, making it useful for testing OCR services or document processing workflows that expect image-based PDFs.

## Base URL

```
http://localhost:34001/v3/ocr/api/pdf-converter
```

## Endpoints

### 1. Convert PDF to Scanned PDF

**POST** `/convert-to-scanned`

Converts a regular PDF document into a "scanned" PDF where each page is rendered as an image.

#### Request

- **Content-Type**: `multipart/form-data`
- **Parameters**:
  - `file` (required): PDF file to convert
  - `dpi` (optional): Resolution for rendering pages (default: 300)
    - Minimum: 150
    - Recommended: 300
    - Maximum: 600

#### Response

- **Content-Type**: `application/pdf`
- **Headers**:
  - `Content-Disposition`: `attachment; filename="scanned_[original-filename].pdf"`
  - `X-Processing-Time-Ms`: Processing time in milliseconds
  - `X-Original-Size-Bytes`: Original file size
  - `X-Output-Size-Bytes`: Converted file size
  - `X-DPI`: DPI used for conversion

#### Example Usage

```bash
# Convert with default DPI (300)
curl -X POST \
  http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@./document.pdf' \
  --output scanned_document.pdf

# Convert with custom DPI
curl -X POST \
  http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned \
  -H 'Content-Type: multipart/form-data' \
  -F 'file=@./document.pdf' \
  -F 'dpi=600' \
  --output high_quality_scanned.pdf
```

#### Error Responses

- **400 Bad Request**: Invalid input (empty file, not a PDF, invalid DPI)
- **500 Internal Server Error**: Processing error

### 2. Get Service Information

**GET** `/info`

Returns information about the PDF conversion service.

#### Response

```json
{
  "service": "PDF to Scanned PDF Converter",
  "description": "Converts regular PDF documents into 'scanned' PDFs where each page is rendered as an image",
  "version": "1.0.0",
  "supportedFormats": {
    "input": "PDF (application/pdf)",
    "output": "PDF (application/pdf) with image-based pages"
  },
  "dpiSettings": {
    "minimum": 150,
    "default": 300,
    "recommended": 300,
    "maximum": 600,
    "description": "Higher DPI = better quality but larger file size"
  },
  "useCases": [
    "Testing OCR services with scanned documents",
    "Document processing workflows expecting image-based PDFs",
    "Simulating physical document scanning",
    "Converting text-based PDFs to image-based PDFs for security"
  ],
  "endpoints": {
    "convert": "/api/pdf-converter/convert-to-scanned",
    "info": "/api/pdf-converter/info"
  }
}
```

## DPI Settings Guide

| DPI | Quality | File Size | Use Case |
|-----|---------|-----------|----------|
| 150 | Basic | Small | Quick testing, low storage requirements |
| 300 | Standard | Medium | **Recommended** - Good balance of quality and size |
| 600 | High | Large | High-quality documents, detailed analysis |

## Use Cases

### 1. Testing OCR Services
Convert text-based PDFs to image-based PDFs to test OCR accuracy and performance.

### 2. Document Processing Workflows
Simulate scanned documents for workflows that expect image-based PDFs.

### 3. Security/Privacy
Convert text-based PDFs to image-based PDFs to prevent text extraction.

### 4. Legacy System Integration
Convert modern PDFs to formats expected by older document processing systems.

## Integration Examples

### JavaScript/Frontend

```javascript
async function convertPdfToScanned(file, dpi = 300) {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('dpi', dpi);

  const response = await fetch('/v3/ocr/api/pdf-converter/convert-to-scanned', {
    method: 'POST',
    body: formData
  });

  if (response.ok) {
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    
    // Download the file
    const a = document.createElement('a');
    a.href = url;
    a.download = `scanned_${file.name}`;
    a.click();
  } else {
    console.error('Conversion failed:', await response.text());
  }
}
```

### Java/Spring Boot

```java
@Service
public class PdfConversionService {
    
    private final RestTemplate restTemplate;
    
    public byte[] convertToScannedPdf(byte[] pdfBytes, int dpi) {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", new ByteArrayResource(pdfBytes) {
            @Override
            public String getFilename() {
                return "document.pdf";
            }
        });
        body.add("dpi", dpi);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        
        HttpEntity<MultiValueMap<String, Object>> requestEntity = 
            new HttpEntity<>(body, headers);
        
        ResponseEntity<byte[]> response = restTemplate.exchange(
            "http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned",
            HttpMethod.POST,
            requestEntity,
            byte[].class
        );
        
        return response.getBody();
    }
}
```

### Python

```python
import requests

def convert_pdf_to_scanned(file_path, dpi=300):
    url = "http://localhost:34001/v3/ocr/api/pdf-converter/convert-to-scanned"
    
    with open(file_path, 'rb') as file:
        files = {'file': file}
        data = {'dpi': dpi}
        
        response = requests.post(url, files=files, data=data)
        
        if response.status_code == 200:
            output_path = f"scanned_{file_path}"
            with open(output_path, 'wb') as output_file:
                output_file.write(response.content)
            return output_path
        else:
            raise Exception(f"Conversion failed: {response.text}")
```

## Error Handling

The API returns appropriate HTTP status codes and error messages:

### 400 Bad Request
```json
{
  "error": "Invalid input: File must be a PDF document. Received content type: text/plain",
  "timestamp": "2025-08-14T23:30:00.000Z"
}
```

### 500 Internal Server Error
```json
{
  "error": "Failed to process PDF: [specific error message]",
  "timestamp": "2025-08-14T23:30:00.000Z"
}
```

## Performance Considerations

- **File Size Limit**: 50MB maximum
- **Processing Time**: Varies based on:
  - Number of pages
  - DPI setting
  - Page complexity
  - Server resources
- **Memory Usage**: Higher DPI settings require more memory
- **Output Size**: Higher DPI results in larger output files

## Swagger/OpenAPI Documentation

The API is fully documented with Swagger annotations. Access the interactive documentation at:

```
http://localhost:34001/v3/ocr/swagger-ui.html
```

## Testing

Use the provided HTTP test file (`pdf-converter-api.http`) with your IDE or REST client to test the API endpoints.
