package com.vitality.ai.ocr.controller;

import com.vitality.ai.ocr.service.PdfToScannedPdfService;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.PDPage;
import org.apache.pdfbox.pdmodel.PDPageContentStream;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.pdmodel.font.PDType1Font;
import org.apache.pdfbox.pdmodel.font.Standard14Fonts;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.core.io.Resource;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(PdfConverterController.class)
class PdfConverterControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private PdfToScannedPdfService pdfToScannedPdfService;

    private byte[] samplePdfBytes;
    private byte[] mockScannedPdfBytes;

    @BeforeEach
    void setUp() throws IOException {
        // Create a sample PDF for testing
        samplePdfBytes = createSamplePdf();
        
        // Mock scanned PDF response
        mockScannedPdfBytes = "mock-scanned-pdf-content".getBytes();
    }

    @Test
    void testConvertToScannedPdf_Success() throws Exception {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "test.pdf", 
                "application/pdf", 
                samplePdfBytes
        );

        when(pdfToScannedPdfService.convertToScannedPdf(any(byte[].class), eq(300)))
                .thenReturn(mockScannedPdfBytes);

        // Act & Assert
        MvcResult result = mockMvc.perform(multipart("/api/pdf-converter/convert-to-scanned")
                        .file(file)
                        .param("dpi", "300"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_PDF))
                .andExpect(header().string("Content-Disposition", "attachment; filename=\"scanned_test.pdf\""))
                .andExpect(header().exists("X-Processing-Time-Ms"))
                .andExpect(header().exists("X-Original-Size-Bytes"))
                .andExpect(header().exists("X-Output-Size-Bytes"))
                .andExpect(header().string("X-DPI", "300"))
                .andReturn();

        // Verify the response body
        byte[] responseBytes = result.getResponse().getContentAsByteArray();
        assertArrayEquals(mockScannedPdfBytes, responseBytes);

        // Verify service was called
        verify(pdfToScannedPdfService).convertToScannedPdf(any(byte[].class), eq(300));
    }

    @Test
    void testConvertToScannedPdf_DefaultDpi() throws Exception {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "document.pdf", 
                "application/pdf", 
                samplePdfBytes
        );

        when(pdfToScannedPdfService.convertToScannedPdf(any(byte[].class), eq(300)))
                .thenReturn(mockScannedPdfBytes);

        // Act & Assert
        mockMvc.perform(multipart("/api/pdf-converter/convert-to-scanned")
                        .file(file))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_PDF))
                .andExpect(header().string("Content-Disposition", "attachment; filename=\"scanned_document.pdf\""))
                .andExpect(header().string("X-DPI", "300"));

        verify(pdfToScannedPdfService).convertToScannedPdf(any(byte[].class), eq(300));
    }

    @Test
    void testConvertToScannedPdf_CustomDpi() throws Exception {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "high-quality.pdf", 
                "application/pdf", 
                samplePdfBytes
        );

        when(pdfToScannedPdfService.convertToScannedPdf(any(byte[].class), eq(600)))
                .thenReturn(mockScannedPdfBytes);

        // Act & Assert
        mockMvc.perform(multipart("/api/pdf-converter/convert-to-scanned")
                        .file(file)
                        .param("dpi", "600"))
                .andExpect(status().isOk())
                .andExpect(header().string("X-DPI", "600"));

        verify(pdfToScannedPdfService).convertToScannedPdf(any(byte[].class), eq(600));
    }

    @Test
    void testConvertToScannedPdf_EmptyFile() throws Exception {
        // Arrange
        MockMultipartFile emptyFile = new MockMultipartFile(
                "file", 
                "empty.pdf", 
                "application/pdf", 
                new byte[0]
        );

        // Act & Assert
        mockMvc.perform(multipart("/api/pdf-converter/convert-to-scanned")
                        .file(emptyFile))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(pdfToScannedPdfService, never()).convertToScannedPdf(any(byte[].class), anyInt());
    }

    @Test
    void testConvertToScannedPdf_InvalidContentType() throws Exception {
        // Arrange
        MockMultipartFile textFile = new MockMultipartFile(
                "file", 
                "document.txt", 
                "text/plain", 
                "This is not a PDF".getBytes()
        );

        // Act & Assert
        mockMvc.perform(multipart("/api/pdf-converter/convert-to-scanned")
                        .file(textFile))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(pdfToScannedPdfService, never()).convertToScannedPdf(any(byte[].class), anyInt());
    }

    @Test
    void testConvertToScannedPdf_InvalidDpi() throws Exception {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "test.pdf", 
                "application/pdf", 
                samplePdfBytes
        );

        // Act & Assert
        mockMvc.perform(multipart("/api/pdf-converter/convert-to-scanned")
                        .file(file)
                        .param("dpi", "100"))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(pdfToScannedPdfService, never()).convertToScannedPdf(any(byte[].class), anyInt());
    }

    @Test
    void testConvertToScannedPdf_ServiceException() throws Exception {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
                "file", 
                "corrupted.pdf", 
                "application/pdf", 
                samplePdfBytes
        );

        when(pdfToScannedPdfService.convertToScannedPdf(any(byte[].class), anyInt()))
                .thenThrow(new IOException("Failed to process PDF"));

        // Act & Assert
        mockMvc.perform(multipart("/api/pdf-converter/convert-to-scanned")
                        .file(file))
                .andExpect(status().isInternalServerError())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON));

        verify(pdfToScannedPdfService).convertToScannedPdf(any(byte[].class), eq(300));
    }

    @Test
    void testGetServiceInfo() throws Exception {
        // Act & Assert
        mockMvc.perform(get("/api/pdf-converter/info"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.service").value("PDF to Scanned PDF Converter"))
                .andExpect(jsonPath("$.version").value("1.0.0"))
                .andExpect(jsonPath("$.dpiSettings.default").value(300))
                .andExpect(jsonPath("$.dpiSettings.minimum").value(150))
                .andExpect(jsonPath("$.dpiSettings.maximum").value(600))
                .andExpect(jsonPath("$.supportedFormats.input").value("PDF (application/pdf)"))
                .andExpect(jsonPath("$.endpoints.convert").value("/api/pdf-converter/convert-to-scanned"))
                .andExpect(jsonPath("$.endpoints.info").value("/api/pdf-converter/info"));
    }

    @Test
    void testConvertToScannedPdf_FilenameGeneration() throws Exception {
        // Test various filename scenarios
        String[][] testCases = {
                {"document.pdf", "scanned_document.pdf"},
                {"Document.PDF", "scanned_Document.pdf"},
                {"my-file", "scanned_my-file.pdf"},
                {"", "scanned_document.pdf"},
                {null, "scanned_document.pdf"}
        };

        for (String[] testCase : testCases) {
            String inputFilename = testCase[0];
            String expectedOutputFilename = testCase[1];

            MockMultipartFile file = new MockMultipartFile(
                    "file", 
                    inputFilename, 
                    "application/pdf", 
                    samplePdfBytes
            );

            when(pdfToScannedPdfService.convertToScannedPdf(any(byte[].class), anyInt()))
                    .thenReturn(mockScannedPdfBytes);

            mockMvc.perform(multipart("/api/pdf-converter/convert-to-scanned")
                            .file(file))
                    .andExpect(status().isOk())
                    .andExpect(header().string("Content-Disposition", 
                            "attachment; filename=\"" + expectedOutputFilename + "\""));
        }
    }

    /**
     * Helper method to create a sample PDF for testing.
     */
    private byte[] createSamplePdf() throws IOException {
        try (PDDocument document = new PDDocument()) {
            PDPage page = new PDPage(PDRectangle.A4);
            document.addPage(page);

            try (PDPageContentStream contentStream = new PDPageContentStream(document, page)) {
                contentStream.beginText();
                contentStream.setFont(new PDType1Font(Standard14Fonts.FontName.HELVETICA), 12);
                contentStream.newLineAtOffset(100, 700);
                contentStream.showText("Test PDF Document for Controller Testing");
                contentStream.endText();
            }

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.save(outputStream);
            return outputStream.toByteArray();
        }
    }
}
