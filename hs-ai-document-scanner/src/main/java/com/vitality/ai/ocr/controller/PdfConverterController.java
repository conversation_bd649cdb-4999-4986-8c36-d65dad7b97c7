package com.vitality.ai.ocr.controller;

import com.vitality.ai.ocr.service.PdfToScannedPdfService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Map;

/**
 * REST Controller for converting regular PDFs to "scanned" PDFs.
 * This controller accepts PDF files and returns them as image-based PDFs,
 * simulating the effect of physically scanning a document.
 */
@RestController
@RequestMapping("/api/pdf")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "PDF Converter", description = "Convert regular PDFs to scanned PDFs")
public class PdfConverterController {

    private final PdfToScannedPdfService pdfToScannedPdfService;

    /**
     * Convert a regular PDF to a "scanned" PDF where each page is rendered as an image.
     * This is useful for testing OCR services or document processing workflows that expect scanned documents.
     *
     * @param file The PDF file to convert
     * @param dpi  The resolution for rendering pages (optional, default: 300)
     * @return The converted "scanned" PDF as a downloadable file
     */
    @Operation(
        summary = "Convert PDF to Scanned PDF",
        description = "Converts a regular PDF document into a 'scanned' PDF where each page is rendered as a high-resolution image. " +
            "This simulates the effect of physically scanning a document, making it useful for testing OCR services " +
            "or document processing workflows that expect image-based PDFs."
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "PDF successfully converted to scanned format",
            content = @Content(
                mediaType = "application/pdf",
                schema = @Schema(type = "string", format = "binary", description = "Scanned PDF file")
            )
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input - file is empty, not a PDF, or invalid DPI",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error during PDF conversion",
            content = @Content(
                mediaType = "application/json",
                schema = @Schema(implementation = Map.class)
            )
        )
    })
    @PostMapping(value = "/convert-to-scanned", consumes = "multipart/form-data", produces = "application/pdf")
    public ResponseEntity<Resource> convertToScannedPdf(
        @Parameter(
            description = "PDF file to convert to scanned format",
            required = true,
            content = @Content(mediaType = "application/pdf")
        )
        @RequestParam("file") MultipartFile file,

        @Parameter(
            description = "Resolution for rendering pages in DPI (dots per inch). " +
                "Higher values produce better quality but larger files. " +
                "Minimum: 150, Recommended: 300, Maximum: 600",
            example = "300"
        )
        @RequestParam(value = "dpi", defaultValue = "300") int dpi) {

        log.info("POST /api/pdf-converter/convert-to-scanned - Converting PDF: {} (size: {} bytes) at {} DPI",
            file.getOriginalFilename(), file.getSize(), dpi);

        try {
            // Validate input
            validateInput(file, dpi);

            // Convert to scanned PDF
            long startTime = System.currentTimeMillis();
            byte[] scannedPdfBytes = pdfToScannedPdfService.convertToScannedPdf(file.getBytes(), dpi);
            long duration = System.currentTimeMillis() - startTime;

            // Prepare response
            String originalFilename = file.getOriginalFilename();
            String outputFilename = generateOutputFilename(originalFilename);

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_PDF);
            headers.setContentDispositionFormData("attachment", outputFilename);
            headers.add("X-Processing-Time-Ms", String.valueOf(duration));
            headers.add("X-Original-Size-Bytes", String.valueOf(file.getSize()));
            headers.add("X-Output-Size-Bytes", String.valueOf(scannedPdfBytes.length));
            headers.add("X-DPI", String.valueOf(dpi));

            log.info("Successfully converted PDF: {} -> {} ({}ms, {} -> {} bytes)",
                originalFilename, outputFilename, duration, file.getSize(), scannedPdfBytes.length);

            return ResponseEntity.ok()
                .headers(headers)
                .body(new ByteArrayResource(scannedPdfBytes));

        } catch (IllegalArgumentException e) {
            log.warn("Invalid input for PDF conversion: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .contentType(MediaType.APPLICATION_JSON)
                .body(new ByteArrayResource(createErrorResponse("Invalid input: " + e.getMessage()).getBytes()));

        } catch (IOException e) {
            log.error("IO error during PDF conversion: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .contentType(MediaType.APPLICATION_JSON)
                .body(new ByteArrayResource(createErrorResponse("Failed to process PDF: " + e.getMessage()).getBytes()));

        } catch (Exception e) {
            log.error("Unexpected error during PDF conversion: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .contentType(MediaType.APPLICATION_JSON)
                .body(new ByteArrayResource(createErrorResponse("Internal server error during PDF conversion").getBytes()));
        }
    }

    /**
     * Validate the input file and parameters.
     */
    private void validateInput(MultipartFile file, int dpi) {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("File cannot be null or empty");
        }

        String contentType = file.getContentType();
        if (contentType == null || !contentType.toLowerCase().contains("pdf")) {
            throw new IllegalArgumentException("File must be a PDF document. Received content type: " + contentType);
        }

        if (file.getSize() == 0) {
            throw new IllegalArgumentException("File is empty");
        }

        if (file.getSize() > 50 * 1024 * 1024) { // 50MB limit
            throw new IllegalArgumentException("File size exceeds maximum limit of 50MB");
        }

        if (dpi < 150) {
            throw new IllegalArgumentException("DPI must be at least 150 for readable output");
        }

        if (dpi > 600) {
            log.warn("High DPI ({}) may result in very large file sizes and slow processing", dpi);
        }
    }

    /**
     * Generate output filename based on input filename.
     */
    private String generateOutputFilename(String originalFilename) {
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            return "scanned_document.pdf";
        }

        String baseName = originalFilename;
        if (baseName.toLowerCase().endsWith(".pdf")) {
            baseName = baseName.substring(0, baseName.length() - 4);
        }

        return "scanned_" + baseName + ".pdf";
    }

    /**
     * Create a JSON error response.
     */
    private String createErrorResponse(String message) {
        return String.format("{\"error\": \"%s\", \"timestamp\": \"%s\"}",
            message.replace("\"", "\\\""),
            java.time.Instant.now().toString());
    }
}
